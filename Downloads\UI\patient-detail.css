/* Patient Detail Page - Mobile-first responsive design */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.4;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: 100%;
    margin: 0 auto;
    background-color: #ffffff;
    min-height: 100vh;
}

/* Header Styles */
.header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.back-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #333;
    padding: 8px;
    margin-right: 12px;
    cursor: pointer;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
}

.back-btn:hover {
    background-color: #f5f5f5;
}

.patient-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* Tab Navigation */
.tab-nav {
    display: flex;
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.tab-btn {
    flex: 1;
    min-width: 80px;
    padding: 16px 12px;
    background: none;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    position: relative;
    white-space: nowrap;
    transition: color 0.2s;
}

.tab-btn.active {
    color: #d32f2f; /* Red color for active tab */
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #d32f2f;
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

/* Patient Information Section */
.patient-info-section {
    margin-bottom: 24px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
    border-bottom: none;
}

.label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    min-width: 80px;
}

.value {
    font-size: 14px;
    color: #333;
    text-align: right;
    flex: 1;
    margin-left: 16px;
}

/* Mark as seen button */
.mark-seen-btn {
    width: 100%;
    padding: 14px;
    background-color: #ffffff;
    border: 2px solid #4a90e2; /* Blue border */
    color: #4a90e2;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: all 0.2s;
    min-height: 48px;
}

.mark-seen-btn:hover {
    background-color: #4a90e2;
    color: #ffffff;
}

/* Action Icons */
.action-icons {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 32px;
    padding: 16px 0;
}

.action-icon {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 12px;
    border-radius: 50%;
    min-width: 48px;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.action-icon:hover {
    background-color: #f5f5f5;
    color: #333;
}

/* Codes Section */
.codes-section {
    margin-bottom: 32px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.add-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #ffffff;
    cursor: pointer;
    transition: transform 0.2s;
}

.add-btn:hover {
    transform: scale(1.1);
}

.cpt-add {
    background-color: #4a90e2; /* Blue for CPT */
}

.icd-add {
    background-color: #d32f2f; /* Red for ICD */
}

/* Code Items */
.code-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 8px;
    margin-bottom: 12px;
}

.delete-code-btn {
    background: none;
    border: none;
    color: #999;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: color 0.2s;
}

.delete-code-btn:hover {
    color: #d32f2f;
}

.code-content {
    flex: 1;
}

.code-text {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 8px;
}

.modify-btn {
    background: none;
    border: none;
    color: #4a90e2;
    font-size: 13px;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
}

/* Add New Codes Section */
.add-codes-section {
    margin-top: 32px;
}

.add-new-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background-color: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: background-color 0.2s;
    min-height: 56px;
}

.add-new-btn:hover {
    background-color: #f0f0f0;
}

.add-new-btn i:first-child {
    font-size: 20px;
    margin-right: 12px;
}

.cpt-btn i:first-child {
    color: #4a90e2;
}

.icd-btn i:first-child {
    color: #d32f2f;
}

.add-new-btn span {
    flex: 1;
    text-align: left;
    font-weight: 500;
}

.add-new-btn i:last-child {
    color: #999;
    font-size: 14px;
}

/* Placeholder content */
.placeholder-content {
    padding: 40px 20px;
    text-align: center;
    color: #666;
}

/* Responsive Design */
@media (min-width: 768px) {
    .container {
        max-width: 768px;
        margin: 20px auto;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .tab-content {
        padding: 32px;
    }
    
    .action-icons {
        gap: 60px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 900px;
    }
}
