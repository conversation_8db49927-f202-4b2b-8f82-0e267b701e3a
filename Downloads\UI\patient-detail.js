// Patient Detail Page JavaScript - Mobile-optimized

document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
    
    // Back button functionality
    document.querySelector('.back-btn').addEventListener('click', function() {
        // In a real app, this would navigate back
        window.history.back();
    });
    
    // Mark as seen button
    document.querySelector('.mark-seen-btn').addEventListener('click', function() {
        this.style.transform = 'scale(0.98)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
        
        // Change button state
        this.textContent = 'Marked as seen ✓';
        this.style.backgroundColor = '#4a90e2';
        this.style.color = '#ffffff';
        this.style.borderColor = '#4a90e2';
        
        // Reset after 2 seconds
        setTimeout(() => {
            this.textContent = 'Mark as seen';
            this.style.backgroundColor = '#ffffff';
            this.style.color = '#4a90e2';
            this.style.borderColor = '#4a90e2';
        }, 2000);
    });
    
    // Action icons
    document.querySelectorAll('.action-icon').forEach(icon => {
        icon.addEventListener('click', function() {
            // Add touch feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            // Handle different actions based on icon
            const iconClass = this.querySelector('i').className;
            if (iconClass.includes('clock')) {
                alert('Time-related action');
            } else if (iconClass.includes('calendar')) {
                alert('Calendar action');
            } else if (iconClass.includes('paperclip')) {
                alert('Attachment action');
            }
        });
    });
    
    // Delete code buttons
    document.querySelectorAll('.delete-code-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete this code?')) {
                this.closest('.code-item').style.opacity = '0';
                this.closest('.code-item').style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    this.closest('.code-item').remove();
                }, 300);
            }
        });
    });
    
    // Modify buttons
    document.querySelectorAll('.modify-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('Modify code functionality would open here');
        });
    });
    
    // Add buttons in section headers
    document.querySelectorAll('.add-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            if (this.classList.contains('cpt-add')) {
                alert('Add CPT code functionality');
            } else if (this.classList.contains('icd-add')) {
                alert('Add ICD code functionality');
            }
        });
    });
    
    // Add new codes buttons
    document.querySelectorAll('.add-new-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Add visual feedback
            this.style.backgroundColor = '#e8e8e8';
            setTimeout(() => {
                this.style.backgroundColor = '#fafafa';
            }, 200);
            
            if (this.classList.contains('cpt-btn')) {
                alert('Navigate to CPT codes selection');
            } else if (this.classList.contains('icd-btn')) {
                alert('Navigate to ICD codes selection');
            }
        });
    });
    
    // Touch optimization for iOS Safari
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        document.body.style.webkitTouchCallout = 'none';
        document.body.style.webkitUserSelect = 'none';
    }
    
    // Prevent zoom on double tap for better mobile experience
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);
    
    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            // Recalculate any layout-dependent elements
            const tabNav = document.querySelector('.tab-nav');
            if (tabNav.scrollWidth > tabNav.clientWidth) {
                tabNav.style.justifyContent = 'flex-start';
            } else {
                tabNav.style.justifyContent = 'center';
            }
        }, 100);
    });
});

// Utility functions
function addTouchFeedback(element) {
    element.addEventListener('touchstart', function() {
        this.style.opacity = '0.7';
    });
    
    element.addEventListener('touchend', function() {
        this.style.opacity = '1';
    });
    
    element.addEventListener('touchcancel', function() {
        this.style.opacity = '1';
    });
}

// Apply touch feedback to all interactive elements
document.addEventListener('DOMContentLoaded', function() {
    const interactiveElements = document.querySelectorAll('button, .tab-btn, .modify-btn');
    interactiveElements.forEach(addTouchFeedback);
});
