<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Details - Rabbit Orange F</title>
    <link rel="stylesheet" href="patient-detail.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <button class="back-btn">
                <i class="fas fa-chevron-left"></i>
            </button>
            <h1 class="patient-name">Rabbit Orange F (65Y)</h1>
        </header>

        <!-- Tab Navigation -->
        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="details">Details</button>
            <button class="tab-btn" data-tab="history">History</button>
            <button class="tab-btn" data-tab="note">Note</button>
            <button class="tab-btn" data-tab="attachments">Attachments</button>
        </nav>

        <!-- Details Tab Content -->
        <div class="tab-content active" id="details">
            <!-- Patient Information -->
            <div class="patient-info-section">
                <div class="info-row">
                    <span class="label">Account</span>
                    <span class="value">************</span>
                </div>
                <div class="info-row">
                    <span class="label">Room</span>
                    <span class="value">0220T-B</span>
                </div>
                <div class="info-row">
                    <span class="label">Type</span>
                    <span class="value">Inpatient</span>
                </div>
                <div class="info-row">
                    <span class="label">DOB</span>
                    <span class="value">11/17/1985</span>
                </div>
                <div class="info-row">
                    <span class="label">LOS</span>
                    <span class="value">LOS:2 | GLOS:4</span>
                </div>
                <div class="info-row">
                    <span class="label">Attending</span>
                    <span class="value">Tiger Cantaloupe, Cheetah Apple</span>
                </div>
                <div class="info-row">
                    <span class="label">Payor</span>
                    <span class="value">Humana Gold Care</span>
                </div>
            </div>

            <!-- Mark as seen button -->
            <button class="mark-seen-btn">Mark as seen</button>

            <!-- Action Icons -->
            <div class="action-icons">
                <button class="action-icon">
                    <i class="far fa-clock"></i>
                </button>
                <button class="action-icon">
                    <i class="far fa-calendar"></i>
                </button>
                <button class="action-icon">
                    <i class="fas fa-paperclip"></i>
                </button>
            </div>

            <!-- CPT Codes Section -->
            <div class="codes-section">
                <div class="section-header">
                    <h3>CPT codes</h3>
                    <button class="add-btn cpt-add">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                
                <div class="code-item">
                    <button class="delete-code-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                    <div class="code-content">
                        <div class="code-text">99396 - Preventive medicine visit, established patient, ages 40-64.</div>
                        <button class="modify-btn">Add/Edit modifiers</button>
                    </div>
                </div>

                <div class="code-item">
                    <button class="delete-code-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                    <div class="code-content">
                        <div class="code-text">28270 - Arthrodesis, great toe.</div>
                        <button class="modify-btn">Add/Edit modifiers</button>
                    </div>
                </div>
            </div>

            <!-- ICD Codes Section -->
            <div class="codes-section">
                <div class="section-header">
                    <h3>ICD codes</h3>
                    <button class="add-btn icd-add">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                
                <div class="code-item">
                    <button class="delete-code-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                    <div class="code-content">
                        <div class="code-text">I10 - Essential (primary) hypertension: A condition characterized by consistently elevated blood pressure...</div>
                    </div>
                </div>

                <div class="code-item">
                    <button class="delete-code-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                    <div class="code-content">
                        <div class="code-text">E11.9 - Type 2 diabetes mellitus without complications: A chronic condition that affects the...</div>
                    </div>
                </div>
            </div>

            <!-- Add New Codes Buttons -->
            <div class="add-codes-section">
                <button class="add-new-btn cpt-btn">
                    <i class="fas fa-plus-circle"></i>
                    <span>Add new CPT codes</span>
                    <i class="fas fa-chevron-right"></i>
                </button>
                
                <button class="add-new-btn icd-btn">
                    <i class="fas fa-plus-circle"></i>
                    <span>Add new ICD codes</span>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>

        <!-- Other tab contents (placeholder) -->
        <div class="tab-content" id="history">
            <div class="placeholder-content">
                <p>History content would go here</p>
            </div>
        </div>

        <div class="tab-content" id="note">
            <div class="placeholder-content">
                <p>Note content would go here</p>
            </div>
        </div>

        <div class="tab-content" id="attachments">
            <div class="placeholder-content">
                <p>Attachments content would go here</p>
            </div>
        </div>
    </div>

    <script src="patient-detail.js"></script>
</body>
</html>
